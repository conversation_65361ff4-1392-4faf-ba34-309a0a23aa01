// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2025 Morpho Association
pragma solidity ^0.8.0;

import {BaseTest} from "./BaseTest.sol";
import {AdapterMock} from "./mocks/AdapterMock.sol";
import {IVaultV2} from "../src/interfaces/IVaultV2.sol";
import {ErrorsLib} from "../src/libraries/ErrorsLib.sol";
import {EventsLib} from "../src/libraries/EventsLib.sol";
import {MathLib} from "../src/libraries/MathLib.sol";
import {WAD} from "../src/libraries/ConstantsLib.sol";
import {console} from "../lib/forge-std/src/console.sol";

/**
 * @title Allocation Multiplication Vulnerability POC
 * @notice Demonstrates critical accounting bug where allocation amounts are incorrectly 
 *         applied to each ID individually rather than being distributed across them
 * @dev This POC proves the vulnerability affects allocateInternal, deallocateInternal, 
 *      and realizeLoss functions, causing systematic cap system corruption
 */
contract AllocationMultiplicationVulnerabilityPOC is BaseTest {
    using MathLib for uint256;

    AdapterMock public adapter;
    bytes32[] public testIds;
    
    // Test constants
    uint256 constant ALLOCATION_AMOUNT = 100e18;
    uint256 constant LOSS_AMOUNT = 10e18;
    uint256 constant LARGE_CAP = type(uint128).max;

    function setUp() public override {
        super.setUp();

        // Create adapter that returns multiple IDs (simulating risk factors)
        adapter = new AdapterMock(address(vault));
        
        // Register adapter
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter), true)));
        vault.setIsAdapter(address(adapter), true);

        // Get the IDs that the adapter returns (id-0 and id-1)
        testIds = new bytes32[](2);
        testIds[0] = keccak256("id-0");
        testIds[1] = keccak256("id-1");

        // Setup caps for both IDs
        increaseAbsoluteCap("id-0", LARGE_CAP);
        increaseAbsoluteCap("id-1", LARGE_CAP);
        increaseRelativeCap("id-0", WAD);
        increaseRelativeCap("id-1", WAD);

        // Provide liquidity to vault
        deal(address(underlyingToken), address(this), 10000e18);
        underlyingToken.approve(address(vault), type(uint256).max);
        vault.deposit(1000e18, address(this));
    }

    /**
     * @notice POC 1: Demonstrates allocation multiplication bug
     * @dev Shows that a single allocation gets added to each ID individually
     */
    function testAllocationMultiplicationBug() public {
        console.log("=== ALLOCATION MULTIPLICATION BUG POC ===");
        
        // Record initial state
        uint256 initialAllocation0 = vault.allocation(testIds[0]);
        uint256 initialAllocation1 = vault.allocation(testIds[1]);
        
        console.log("Initial allocation ID-0:", initialAllocation0);
        console.log("Initial allocation ID-1:", initialAllocation1);
        console.log("Allocating amount:", ALLOCATION_AMOUNT);

        // Perform allocation
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);

        // Check final state - THIS IS THE BUG!
        uint256 finalAllocation0 = vault.allocation(testIds[0]);
        uint256 finalAllocation1 = vault.allocation(testIds[1]);
        
        console.log("Final allocation ID-0:", finalAllocation0);
        console.log("Final allocation ID-1:", finalAllocation1);
        console.log("Total recorded allocation:", finalAllocation0 + finalAllocation1);

        // VULNERABILITY PROOF: Each ID gets the full amount instead of splitting it
        assertEq(finalAllocation0, ALLOCATION_AMOUNT, "ID-0 should have full amount (BUG!)");
        assertEq(finalAllocation1, ALLOCATION_AMOUNT, "ID-1 should have full amount (BUG!)");
        
        // This means 100 tokens allocated = 200 tokens recorded in caps!
        uint256 totalRecorded = finalAllocation0 + finalAllocation1;
        assertEq(totalRecorded, ALLOCATION_AMOUNT * 2, "Total recorded is 2x actual allocation");
        
        console.log("BUG CONFIRMED: 100 tokens allocated but 200 tokens recorded in caps!");
    }

    /**
     * @notice POC 2: Demonstrates deallocation multiplication bug
     * @dev Shows phantom deallocation where amount is subtracted from each ID
     */
    function testDeallocationMultiplicationBug() public {
        console.log("=== DEALLOCATION MULTIPLICATION BUG POC ===");
        
        // First allocate to set up the scenario
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);
        
        uint256 beforeDealloc0 = vault.allocation(testIds[0]);
        uint256 beforeDealloc1 = vault.allocation(testIds[1]);
        
        console.log("Before deallocation ID-0:", beforeDealloc0);
        console.log("Before deallocation ID-1:", beforeDealloc1);
        console.log("Deallocating amount:", ALLOCATION_AMOUNT / 2);

        // Perform deallocation of half the amount
        uint256 deallocAmount = ALLOCATION_AMOUNT / 2;
        vm.prank(allocator);
        vault.deallocate(address(adapter), hex"", deallocAmount);

        uint256 afterDealloc0 = vault.allocation(testIds[0]);
        uint256 afterDealloc1 = vault.allocation(testIds[1]);
        
        console.log("After deallocation ID-0:", afterDealloc0);
        console.log("After deallocation ID-1:", afterDealloc1);

        // VULNERABILITY PROOF: Deallocation amount subtracted from EACH ID
        uint256 reduction0 = beforeDealloc0 - afterDealloc0;
        uint256 reduction1 = beforeDealloc1 - afterDealloc1;
        
        assertEq(reduction0, deallocAmount, "ID-0 reduced by full dealloc amount (BUG!)");
        assertEq(reduction1, deallocAmount, "ID-1 reduced by full dealloc amount (BUG!)");
        
        uint256 totalReduction = reduction0 + reduction1;
        console.log("Total reduction from caps:", totalReduction);
        console.log("Actual tokens withdrawn:", deallocAmount);
        
        assertEq(totalReduction, deallocAmount * 2, "Total reduction is 2x actual deallocation");
        
        console.log("BUG CONFIRMED: 50 tokens withdrawn but 100 tokens deducted from caps!");
    }

    /**
     * @notice POC 3: Demonstrates loss realization multiplication bug
     * @dev Shows how losses get subtracted from each ID individually
     */
    function testLossRealizationMultiplicationBug() public {
        console.log("=== LOSS REALIZATION MULTIPLICATION BUG POC ===");
        
        // Setup: Allocate funds first
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);
        
        // Set up a loss in the adapter
        adapter.setLoss(LOSS_AMOUNT);
        
        uint256 beforeLoss0 = vault.allocation(testIds[0]);
        uint256 beforeLoss1 = vault.allocation(testIds[1]);
        
        console.log("Before loss realization ID-0:", beforeLoss0);
        console.log("Before loss realization ID-1:", beforeLoss1);
        console.log("Actual loss amount:", LOSS_AMOUNT);

        // Realize the loss
        vm.prank(allocator);
        vault.realizeLoss(address(adapter), hex"");

        uint256 afterLoss0 = vault.allocation(testIds[0]);
        uint256 afterLoss1 = vault.allocation(testIds[1]);
        
        console.log("After loss realization ID-0:", afterLoss0);
        console.log("After loss realization ID-1:", afterLoss1);

        // VULNERABILITY PROOF: Loss subtracted from EACH ID
        uint256 lossReduction0 = beforeLoss0 - afterLoss0;
        uint256 lossReduction1 = beforeLoss1 - afterLoss1;
        
        assertEq(lossReduction0, LOSS_AMOUNT, "ID-0 reduced by full loss amount (BUG!)");
        assertEq(lossReduction1, LOSS_AMOUNT, "ID-1 reduced by full loss amount (BUG!)");
        
        uint256 totalLossReduction = lossReduction0 + lossReduction1;
        console.log("Total loss deducted from caps:", totalLossReduction);
        console.log("Actual loss incurred:", LOSS_AMOUNT);
        
        assertEq(totalLossReduction, LOSS_AMOUNT * 2, "Total loss deduction is 2x actual loss");
        
        console.log("BUG CONFIRMED: 10 token loss but 20 tokens deducted from caps!");
    }

    /**
     * @notice POC 4: Demonstrates cap bloat and premature limits
     * @dev Shows how the bug causes artificial cap exhaustion
     */
    function testCapBloatAndPrematureLimits() public {
        console.log("=== CAP BLOAT AND PREMATURE LIMITS POC ===");
        
        // Set realistic caps that should allow the allocation
        uint256 realisticCap = 150e18; // Should allow 100 token allocation with buffer
        increaseAbsoluteCap("id-0", realisticCap);
        increaseAbsoluteCap("id-1", realisticCap);
        
        console.log("Set cap for each ID:", realisticCap);
        console.log("Attempting to allocate:", ALLOCATION_AMOUNT);
        console.log("Expected behavior: Should succeed (100 < 150)");

        // This allocation should succeed but will fail due to the bug
        vm.prank(allocator);
        vm.expectRevert(ErrorsLib.AbsoluteCapExceeded.selector);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);
        
        console.log("RESULT: Allocation FAILED due to cap exceeded");
        console.log("BUG CONFIRMED: Caps are artificially inflated, causing premature limits!");
        
        // Show what the system thinks vs reality
        console.log("System thinks each ID needs:", ALLOCATION_AMOUNT);
        console.log("Reality: IDs should share the", ALLOCATION_AMOUNT, "tokens");
    }

    /**
     * @notice POC 5: Demonstrates underflow risk in loss realization
     * @dev Shows potential underflow when loss exceeds individual ID allocation
     */
    function testLossRealizationUnderflowRisk() public {
        console.log("=== LOSS REALIZATION UNDERFLOW RISK POC ===");

        // Allocate a small amount
        uint256 smallAllocation = 50e18;
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", smallAllocation);

        // Set up a loss larger than individual ID allocation but smaller than total
        uint256 largeLoss = 60e18; // > 50 but < 100 (total recorded)
        adapter.setLoss(largeLoss);

        console.log("Allocated amount:", smallAllocation);
        console.log("Each ID recorded allocation:", smallAllocation);
        console.log("Loss amount:", largeLoss);
        console.log("Loss > individual ID allocation:", largeLoss > smallAllocation);

        uint256 beforeLoss0 = vault.allocation(testIds[0]);
        uint256 beforeLoss1 = vault.allocation(testIds[1]);

        console.log("ID-0 allocation before loss:", beforeLoss0);
        console.log("ID-1 allocation before loss:", beforeLoss1);

        // This should cause underflow since 50 - 60 < 0
        vm.prank(allocator);
        vm.expectRevert(); // Expecting underflow revert
        vault.realizeLoss(address(adapter), hex"");

        console.log("BUG CONFIRMED: Loss realization can cause underflow!");
        console.log("Each ID tries to subtract", largeLoss, "from", smallAllocation);
    }

    /**
     * @notice POC 6: Demonstrates accounting corruption persistence
     * @dev Shows how the bug creates permanent inconsistencies
     */
    function testAccountingCorruptionPersistence() public {
        console.log("=== ACCOUNTING CORRUPTION PERSISTENCE POC ===");

        uint256 totalDeposited = 1000e18; // From setUp
        console.log("Total vault assets:", totalDeposited);

        // Perform multiple operations to show cumulative corruption
        vm.startPrank(allocator);

        // First allocation
        vault.allocate(address(adapter), hex"", 100e18);
        uint256 totalRecorded1 = vault.allocation(testIds[0]) + vault.allocation(testIds[1]);
        console.log("After 100 allocation, total recorded:", totalRecorded1);

        // Second allocation
        vault.allocate(address(adapter), hex"", 50e18);
        uint256 totalRecorded2 = vault.allocation(testIds[0]) + vault.allocation(testIds[1]);
        console.log("After additional 50 allocation, total recorded:", totalRecorded2);

        // Deallocation
        vault.deallocate(address(adapter), hex"", 30e18);
        uint256 totalRecorded3 = vault.allocation(testIds[0]) + vault.allocation(testIds[1]);
        console.log("After 30 deallocation, total recorded:", totalRecorded3);

        vm.stopPrank();

        // Calculate the corruption
        uint256 actualAllocated = 100e18 + 50e18 - 30e18; // 120e18
        uint256 recordedAllocated = totalRecorded3;
        uint256 corruption = recordedAllocated - actualAllocated;

        console.log("Actual net allocation:", actualAllocated);
        console.log("Recorded allocation:", recordedAllocated);
        console.log("Accounting corruption:", corruption);

        assertEq(corruption, actualAllocated, "Corruption equals actual allocation (2x multiplier)");
        console.log("BUG CONFIRMED: Permanent 2x accounting corruption!");
    }

    /**
     * @notice POC 7: Demonstrates real-world impact with MorphoMarketV1Adapter pattern
     * @dev Shows how the bug affects actual adapter implementations
     */
    function testRealWorldAdapterImpact() public {
        console.log("=== REAL-WORLD ADAPTER IMPACT POC ===");

        // The existing AdapterMock returns 2 IDs: keccak256("id-0") and keccak256("id-1")
        // This simulates the multi-ID pattern used by real adapters

        console.log("Using AdapterMock with 2 risk factor IDs (simulating real adapter pattern)");
        console.log("Allocating:", ALLOCATION_AMOUNT);

        // Perform allocation using the existing adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);

        // Check the multiplication effect using the correct IDs
        uint256 id0_allocation = vault.allocation(testIds[0]);
        uint256 id1_allocation = vault.allocation(testIds[1]);

        console.log("ID-0 allocation:", id0_allocation);
        console.log("ID-1 allocation:", id1_allocation);

        uint256 totalRecorded = id0_allocation + id1_allocation;
        console.log("Total recorded across all IDs:", totalRecorded);
        console.log("Actual allocation:", ALLOCATION_AMOUNT);
        console.log("Multiplication factor:", totalRecorded / ALLOCATION_AMOUNT);

        assertEq(totalRecorded, ALLOCATION_AMOUNT * 2, "2x multiplication with 2 IDs");
        console.log("BUG CONFIRMED: Real adapters suffer 2x accounting corruption!");
    }

    /**
     * @notice POC 8: Demonstrates that protective mechanisms cannot prevent the vulnerability
     * @dev Tests all safeguards and shows they fail to catch the accounting bug
     */
    function testProtectiveMechanismsCannotPreventVulnerability() public {
        console.log("=== PROTECTIVE MECHANISMS BYPASS POC ===");

        // Test 1: Access controls are properly enforced but don't prevent the bug
        console.log("1. Testing access controls...");

        // Non-allocator cannot call allocate (access control works)
        vm.prank(address(0x999));
        vm.expectRevert(ErrorsLib.Unauthorized.selector);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);
        console.log("   [PASS] Access control works - non-allocator rejected");

        // But authorized allocator still triggers the bug
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);
        uint256 totalRecorded = vault.allocation(testIds[0]) + vault.allocation(testIds[1]);
        console.log("   [FAIL] Authorized allocator still causes 2x multiplication:", totalRecorded);

        // Test 2: Adapter validation works but doesn't prevent the bug
        console.log("2. Testing adapter validation...");

        AdapterMock unauthorizedAdapter = new AdapterMock(address(vault));
        // Unauthorized adapter is rejected (validation works)
        vm.prank(allocator);
        vm.expectRevert(ErrorsLib.NotAdapter.selector);
        vault.allocate(address(unauthorizedAdapter), hex"", ALLOCATION_AMOUNT);
        console.log("   [PASS] Adapter validation works - unauthorized adapter rejected");
        console.log("   [FAIL] But authorized adapters still cause multiplication bug");

        // Test 3: Cap checks work but use corrupted allocation values
        console.log("3. Testing cap enforcement with corrupted values...");

        // Set tight caps that should allow 100 tokens but will fail due to 2x multiplication
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.decreaseAbsoluteCap, ("id-0", 120e18)));
        vault.decreaseAbsoluteCap("id-0", 120e18); // Should allow 100 + some buffer
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.decreaseAbsoluteCap, ("id-1", 120e18)));
        vault.decreaseAbsoluteCap("id-1", 120e18);

        // The cap check works but uses the corrupted 2x allocation value
        // Since each ID gets the full 100 tokens, and cap is 120, this should fail
        vm.prank(allocator);
        vm.expectRevert(ErrorsLib.AbsoluteCapExceeded.selector);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);
        console.log("   [PASS] Cap enforcement works but uses corrupted allocation values");
        console.log("   [FAIL] Cap thinks allocation needs 200 tokens, rejects 100 token allocation");

        // Test 4: Interest accrual works but doesn't fix the multiplication
        console.log("4. Testing interest accrual...");

        // Set higher caps to allow allocation
        increaseAbsoluteCap("id-0", LARGE_CAP);
        increaseAbsoluteCap("id-1", LARGE_CAP);

        // Set interest in adapter
        adapter.setInterest(5e18);

        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", ALLOCATION_AMOUNT);

        uint256 finalAlloc0 = vault.allocation(testIds[0]);
        uint256 finalAlloc1 = vault.allocation(testIds[1]);

        // Interest is also multiplied across IDs
        uint256 expectedPerID = ALLOCATION_AMOUNT + 5e18; // 100 + 5 interest
        assertEq(finalAlloc0, expectedPerID, "Interest also multiplied on ID-0");
        assertEq(finalAlloc1, expectedPerID, "Interest also multiplied on ID-1");

        console.log("   [FAIL] Interest accrual also suffers from multiplication bug");
        console.log("   Each ID gets full allocation + full interest instead of sharing");

        console.log("CONCLUSION: All protective mechanisms work as designed but cannot prevent");
        console.log("the fundamental accounting bug in the allocation loop logic.");
    }

    /**
     * @notice POC 9: Demonstrates the bug persists across different adapter types
     * @dev Shows the vulnerability is systematic, not adapter-specific
     */
    function testVulnerabilityAcrossAdapterTypes() public {
        console.log("=== VULNERABILITY ACROSS ADAPTER TYPES POC ===");

        // Create a second adapter
        AdapterMock adapter2 = new AdapterMock(address(vault));

        // Register the second adapter
        vm.startPrank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter2), true)));
        vault.setIsAdapter(address(adapter2), true);
        vm.stopPrank();

        console.log("Testing allocation across multiple adapters using same IDs...");

        // Both adapters use the same IDs (id-0 and id-1) - this is realistic for shared risk factors

        // Allocate through first adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 50e18);

        uint256 afterAdapter1_id0 = vault.allocation(testIds[0]);
        uint256 afterAdapter1_id1 = vault.allocation(testIds[1]);

        console.log("After adapter1 (50 tokens):");
        console.log("  ID-0:", afterAdapter1_id0);
        console.log("  ID-1:", afterAdapter1_id1);

        // Allocate through second adapter (different adapter, same IDs)
        vm.prank(allocator);
        vault.allocate(address(adapter2), hex"", 30e18);

        uint256 afterAdapter2_id0 = vault.allocation(testIds[0]);
        uint256 afterAdapter2_id1 = vault.allocation(testIds[1]);

        console.log("After adapter2 (30 tokens):");
        console.log("  ID-0:", afterAdapter2_id0);
        console.log("  ID-1:", afterAdapter2_id1);

        // The bug compounds across adapters
        uint256 totalAllocated = 50e18 + 30e18; // 80 tokens
        uint256 totalRecorded = afterAdapter2_id0 + afterAdapter2_id1; // Should be 80, but is 160

        console.log("Total tokens allocated:", totalAllocated);
        console.log("Total recorded in caps:", totalRecorded);
        console.log("Multiplication factor:", totalRecorded / totalAllocated);

        assertEq(totalRecorded, totalAllocated * 2, "2x multiplication persists across adapters");
        console.log("BUG CONFIRMED: Vulnerability is systematic across all adapter types!");
    }
}
