// SPDX-License-Identifier: GPL-2.0-or-later
pragma solidity ^0.8.0;

import {BaseTest} from "./BaseTest.sol";
import {AdapterMock} from "./mocks/AdapterMock.sol";
import {IVaultV2} from "../src/interfaces/IVaultV2.sol";
import {ErrorsLib} from "../src/libraries/ErrorsLib.sol";
import {WAD} from "../src/libraries/ConstantsLib.sol";
import {console} from "../lib/forge-std/src/console.sol";

/**
 * @title Clean Allocation Bug POC
 * @notice Bulletproof demonstration of the allocation multiplication vulnerability
 * @dev This POC focuses on the core issue without complex scenarios
 */
contract CleanAllocationBugPOC is BaseTest {
    
    AdapterMock public adapter;
    bytes32 public id0;
    bytes32 public id1;
    
    function setUp() public override {
        super.setUp();

        // Create and register adapter
        adapter = new AdapterMock(address(vault));
        vm.prank(curator);
        vault.submit(abi.encodeCall(IVaultV2.setIsAdapter, (address(adapter), true)));
        vault.setIsAdapter(address(adapter), true);

        // Get the exact IDs that AdapterMock returns
        id0 = keccak256("id-0");
        id1 = keccak256("id-1");

        // Setup caps
        increaseAbsoluteCap("id-0", type(uint128).max);
        increaseAbsoluteCap("id-1", type(uint128).max);
        increaseRelativeCap("id-0", WAD);
        increaseRelativeCap("id-1", WAD);

        // Add liquidity
        deal(address(underlyingToken), address(this), 10000e18);
        underlyingToken.approve(address(vault), type(uint256).max);
        vault.deposit(1000e18, address(this));
    }

    /**
     * @notice Core POC: Demonstrates the allocation multiplication bug
     * @dev This is the smoking gun - shows 100 tokens become 200 tokens in accounting
     */
    function testCoreAllocationMultiplicationBug() public {
        console.log("=== CORE ALLOCATION MULTIPLICATION BUG ===");
        
        uint256 allocAmount = 100e18;
        
        // Record state before allocation
        uint256 beforeId0 = vault.allocation(id0);
        uint256 beforeId1 = vault.allocation(id1);
        
        console.log("BEFORE ALLOCATION:");
        console.log("  ID-0 allocation:", beforeId0);
        console.log("  ID-1 allocation:", beforeId1);
        console.log("  Total recorded:", beforeId0 + beforeId1);
        console.log("");
        
        console.log("PERFORMING ALLOCATION:");
        console.log("  Amount to allocate:", allocAmount);
        console.log("  Expected behavior: Split between 2 IDs (50 each)");
        console.log("  Actual behavior: Full amount to EACH ID (100 each)");
        console.log("");
        
        // Perform the allocation
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", allocAmount);
        
        // Record state after allocation
        uint256 afterId0 = vault.allocation(id0);
        uint256 afterId1 = vault.allocation(id1);
        uint256 totalRecorded = afterId0 + afterId1;
        
        console.log("AFTER ALLOCATION:");
        console.log("  ID-0 allocation:", afterId0);
        console.log("  ID-1 allocation:", afterId1);
        console.log("  Total recorded:", totalRecorded);
        console.log("");
        
        console.log("VULNERABILITY ANALYSIS:");
        console.log("  Tokens actually allocated:", allocAmount);
        console.log("  Tokens recorded in caps:", totalRecorded);
        console.log("  Multiplication factor:", totalRecorded / allocAmount);
        console.log("  Excess accounting:", totalRecorded - allocAmount);
        
        // THE SMOKING GUN: Each ID gets the full amount instead of sharing it
        assertEq(afterId0, allocAmount, "ID-0 gets full amount (BUG!)");
        assertEq(afterId1, allocAmount, "ID-1 gets full amount (BUG!)");
        assertEq(totalRecorded, allocAmount * 2, "Total is 2x actual allocation (BUG!)");
        
        console.log("");
        console.log(" VULNERABILITY CONFIRMED: 2x ACCOUNTING MULTIPLICATION ");
    }

    /**
     * @notice Demonstrates the deallocation multiplication bug
     */
    function testDeallocationMultiplicationBug() public {
        console.log("=== DEALLOCATION MULTIPLICATION BUG ===");
        
        // First allocate
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 100e18);
        
        uint256 beforeId0 = vault.allocation(id0);
        uint256 beforeId1 = vault.allocation(id1);
        
        console.log("BEFORE DEALLOCATION:");
        console.log("  ID-0 allocation:", beforeId0);
        console.log("  ID-1 allocation:", beforeId1);
        
        // Deallocate half
        uint256 deallocAmount = 50e18;
        console.log("DEALLOCATING:", deallocAmount);
        
        vm.prank(allocator);
        vault.deallocate(address(adapter), hex"", deallocAmount);
        
        uint256 afterId0 = vault.allocation(id0);
        uint256 afterId1 = vault.allocation(id1);
        
        console.log("AFTER DEALLOCATION:");
        console.log("  ID-0 allocation:", afterId0);
        console.log("  ID-1 allocation:", afterId1);
        
        // Each ID should lose only 25 tokens (50/2), but loses 50 tokens each
        uint256 id0Reduction = beforeId0 - afterId0;
        uint256 id1Reduction = beforeId1 - afterId1;
        uint256 totalReduction = id0Reduction + id1Reduction;
        
        console.log("REDUCTION ANALYSIS:");
        console.log("  ID-0 reduced by:", id0Reduction);
        console.log("  ID-1 reduced by:", id1Reduction);
        console.log("  Total reduction:", totalReduction);
        console.log("  Expected reduction:", deallocAmount);
        console.log("  Excess reduction:", totalReduction - deallocAmount);
        
        assertEq(id0Reduction, deallocAmount, "ID-0 reduced by full amount (BUG!)");
        assertEq(id1Reduction, deallocAmount, "ID-1 reduced by full amount (BUG!)");
        assertEq(totalReduction, deallocAmount * 2, "Total reduction is 2x (BUG!)");
        
        console.log(" DEALLOCATION BUG CONFIRMED: 2x REDUCTION ");
    }

    /**
     * @notice Demonstrates loss realization multiplication bug
     */
    function testLossRealizationBug() public {
        console.log("=== LOSS REALIZATION MULTIPLICATION BUG ===");
        
        // Allocate first
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 100e18);
        
        // Set up a loss
        uint256 lossAmount = 10e18;
        adapter.setLoss(lossAmount);
        
        uint256 beforeId0 = vault.allocation(id0);
        uint256 beforeId1 = vault.allocation(id1);
        
        console.log("BEFORE LOSS REALIZATION:");
        console.log("  ID-0 allocation:", beforeId0);
        console.log("  ID-1 allocation:", beforeId1);
        console.log("  Loss amount:", lossAmount);
        
        // Realize the loss
        vm.prank(allocator);
        vault.realizeLoss(address(adapter), hex"");
        
        uint256 afterId0 = vault.allocation(id0);
        uint256 afterId1 = vault.allocation(id1);
        
        uint256 id0Loss = beforeId0 - afterId0;
        uint256 id1Loss = beforeId1 - afterId1;
        uint256 totalLossDeducted = id0Loss + id1Loss;
        
        console.log("AFTER LOSS REALIZATION:");
        console.log("  ID-0 allocation:", afterId0);
        console.log("  ID-1 allocation:", afterId1);
        console.log("  ID-0 loss deducted:", id0Loss);
        console.log("  ID-1 loss deducted:", id1Loss);
        console.log("  Total loss deducted:", totalLossDeducted);
        console.log("  Actual loss:", lossAmount);
        console.log("  Excess loss deduction:", totalLossDeducted - lossAmount);
        
        assertEq(id0Loss, lossAmount, "ID-0 loses full amount (BUG!)");
        assertEq(id1Loss, lossAmount, "ID-1 loses full amount (BUG!)");
        assertEq(totalLossDeducted, lossAmount * 2, "Total loss is 2x (BUG!)");
        
        console.log("LOSS REALIZATION BUG CONFIRMED: 2x LOSS DEDUCTION ");
    }

    /**
     * @notice Shows the cumulative impact over multiple operations
     */
    function testCumulativeCorruption() public {
        console.log("=== CUMULATIVE CORRUPTION ANALYSIS ===");
        
        console.log("OPERATION 1: Allocate 100 tokens");
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 100e18);
        
        uint256 step1_total = vault.allocation(id0) + vault.allocation(id1);
        console.log("  Total recorded:", step1_total, "(Expected: 100, Actual corruption: +100)");
        
        console.log("OPERATION 2: Allocate 50 more tokens");
        vm.prank(allocator);
        vault.allocate(address(adapter), hex"", 50e18);
        
        uint256 step2_total = vault.allocation(id0) + vault.allocation(id1);
        console.log("  Total recorded:", step2_total, "(Expected: 150, Actual corruption: +150)");
        
        console.log("OPERATION 3: Deallocate 30 tokens");
        vm.prank(allocator);
        vault.deallocate(address(adapter), hex"", 30e18);
        
        uint256 step3_total = vault.allocation(id0) + vault.allocation(id1);
        console.log("  Total recorded:", step3_total, "(Expected: 120, Actual corruption: +120)");
        
        uint256 actualNetAllocation = 100e18 + 50e18 - 30e18; // 120e18
        uint256 recordedAllocation = step3_total;
        uint256 corruption = recordedAllocation - actualNetAllocation;
        
        console.log("");
        console.log("FINAL ANALYSIS:");
        console.log("  Actual net allocation:", actualNetAllocation);
        console.log("  Recorded allocation:", recordedAllocation);
        console.log("  Permanent corruption:", corruption);
        console.log("  Corruption ratio:", recordedAllocation / actualNetAllocation);
        
        assertEq(corruption, actualNetAllocation, "Corruption equals actual allocation (2x factor)");
        
        console.log(" PERMANENT 2x ACCOUNTING CORRUPTION CONFIRMED ");
    }
}
